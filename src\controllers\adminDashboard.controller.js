const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const { sendSuccess, sendError } = require('../utils/ApiResponse');
const { User, Product, Order, Review, Category, Provider, Customer, Notification } = require('../models');
const mongoose = require('mongoose');

/**
 * Get comprehensive analytics data for admin dashboard
 */
const getAnalytics = catchAsync(async (req, res) => {
  try {
    const { period = '30d', startDate, endDate } = req.query;
    
    // Calculate date ranges based on period
    let dateFilter = {};
    const now = new Date();
    
    if (startDate && endDate) {
      dateFilter = {
        createdAt: {
          $gte: new Date(startDate),
          $lte: new Date(endDate)
        }
      };
    } else {
      switch (period) {
        case '7d':
          dateFilter = { createdAt: { $gte: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) } };
          break;
        case '30d':
          dateFilter = { createdAt: { $gte: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000) } };
          break;
        case '90d':
          dateFilter = { createdAt: { $gte: new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000) } };
          break;
        case '1y':
          dateFilter = { createdAt: { $gte: new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000) } };
          break;
        default:
          dateFilter = { createdAt: { $gte: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000) } };
      }
    }

    // User growth analytics
    const userGrowth = await User.aggregate([
      { $match: dateFilter },
      {
        $group: {
          _id: {
            date: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
            role: '$role'
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id.date': 1 } }
    ]);

    // Order trends
    const orderTrends = await Order.aggregate([
      { $match: dateFilter },
      {
        $group: {
          _id: {
            date: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
            status: '$status'
          },
          count: { $sum: 1 },
          revenue: { $sum: '$total_price' }
        }
      },
      { $sort: { '_id.date': 1 } }
    ]);

    // Revenue analytics by time period
    const revenueAnalytics = await Order.aggregate([
      { $match: dateFilter },
      {
        $group: {
          _id: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
          totalRevenue: { $sum: '$total_price' },
          orderCount: { $sum: 1 },
          averageOrderValue: { $avg: '$total_price' }
        }
      },
      { $sort: { '_id': 1 } }
    ]);

    // Popular products
    const popularProducts = await Order.aggregate([
      { $match: dateFilter },
      {
        $group: {
          _id: '$product_id',
          orderCount: { $sum: 1 },
          totalQuantity: { $sum: '$quantity' },
          revenue: { $sum: '$total_price' }
        }
      },
      {
        $lookup: {
          from: 'products',
          localField: '_id',
          foreignField: '_id',
          as: 'product'
        }
      },
      { $unwind: '$product' },
      {
        $project: {
          productName: '$product.name',
          orderCount: 1,
          totalQuantity: 1,
          revenue: 1
        }
      },
      { $sort: { orderCount: -1 } },
      { $limit: 10 }
    ]);

    // Provider performance
    const providerPerformance = await Order.aggregate([
      { $match: dateFilter },
      {
        $group: {
          _id: '$provider_id',
          orderCount: { $sum: 1 },
          revenue: { $sum: '$total_price' },
          completedOrders: {
            $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
          }
        }
      },
      {
        $lookup: {
          from: 'providers',
          localField: '_id',
          foreignField: '_id',
          as: 'provider'
        }
      },
      { $unwind: '$provider' },
      {
        $addFields: {
          completionRate: {
            $cond: [
              { $gt: ['$orderCount', 0] },
              { $multiply: [{ $divide: ['$completedOrders', '$orderCount'] }, 100] },
              0
            ]
          }
        }
      },
      {
        $project: {
          providerName: '$provider.name',
          orderCount: 1,
          revenue: 1,
          completedOrders: 1,
          completionRate: { $round: ['$completionRate', 2] }
        }
      },
      { $sort: { revenue: -1 } },
      { $limit: 10 }
    ]);

    // Category performance
    const categoryPerformance = await Order.aggregate([
      { $match: dateFilter },
      {
        $lookup: {
          from: 'products',
          localField: 'product_id',
          foreignField: '_id',
          as: 'product'
        }
      },
      { $unwind: '$product' },
      {
        $lookup: {
          from: 'categories',
          localField: 'product.category',
          foreignField: '_id',
          as: 'category'
        }
      },
      { $unwind: '$category' },
      {
        $group: {
          _id: '$category._id',
          categoryName: { $first: '$category.name' },
          orderCount: { $sum: 1 },
          revenue: { $sum: '$total_price' },
          uniqueProducts: { $addToSet: '$product_id' }
        }
      },
      {
        $addFields: {
          uniqueProductCount: { $size: '$uniqueProducts' }
        }
      },
      {
        $project: {
          categoryName: 1,
          orderCount: 1,
          revenue: 1,
          uniqueProductCount: 1
        }
      },
      { $sort: { revenue: -1 } }
    ]);

    const result = {
      period,
      dateRange: dateFilter,
      userGrowth,
      orderTrends,
      revenueAnalytics,
      popularProducts,
      providerPerformance,
      categoryPerformance
    };

    sendSuccess(res, 'Analytics data fetched successfully', httpStatus.OK, result);
  } catch (error) {
    console.error('Analytics error:', error);
    sendError(res, 'Failed to fetch analytics data', httpStatus.INTERNAL_SERVER_ERROR);
  }
});

/**
 * Get system reports
 */
const getReports = catchAsync(async (req, res) => {
  try {
    const { reportType = 'summary' } = req.query;

    let reportData = {};

    switch (reportType) {
      case 'summary':
        reportData = await getSummaryReport();
        break;
      case 'sales':
        reportData = await getSalesReport();
        break;
      case 'users':
        reportData = await getUsersReport();
        break;
      case 'providers':
        reportData = await getProvidersReport();
        break;
      case 'system':
        reportData = await getSystemHealthReport();
        break;
      default:
        reportData = await getSummaryReport();
    }

    sendSuccess(res, `${reportType} report generated successfully`, httpStatus.OK, {
      reportType,
      generatedAt: new Date(),
      data: reportData
    });
  } catch (error) {
    console.error('Reports error:', error);
    sendError(res, 'Failed to generate report', httpStatus.INTERNAL_SERVER_ERROR);
  }
});

/**
 * Advanced search across all entities
 */
const advancedSearch = catchAsync(async (req, res) => {
  try {
    const { 
      query, 
      entities = ['customers', 'providers', 'products', 'orders'], 
      limit = 10,
      filters = {}
    } = req.query;

    if (!query) {
      return sendError(res, 'Search query is required', httpStatus.BAD_REQUEST);
    }

    const searchResults = {};
    const searchRegex = new RegExp(query, 'i');

    // Search customers
    if (entities.includes('customers')) {
      searchResults.customers = await Customer.aggregate([
        {
          $lookup: {
            from: 'users',
            localField: 'user',
            foreignField: '_id',
            as: 'userDetails'
          }
        },
        { $unwind: '$userDetails' },
        {
          $match: {
            $or: [
              { username: searchRegex },
              { bio: searchRegex },
              { 'userDetails.email': searchRegex }
            ]
          }
        },
        {
          $project: {
            username: 1,
            bio: 1,
            email: '$userDetails.email',
            createdAt: 1,
            photo: 1
          }
        },
        { $limit: parseInt(limit) }
      ]);
    }

    // Search providers
    if (entities.includes('providers')) {
      searchResults.providers = await Provider.aggregate([
        {
          $lookup: {
            from: 'users',
            localField: 'user',
            foreignField: '_id',
            as: 'userDetails'
          }
        },
        { $unwind: '$userDetails' },
        {
          $match: {
            $or: [
              { name: searchRegex },
              { description: searchRegex },
              { city: searchRegex },
              { state: searchRegex },
              { 'userDetails.email': searchRegex }
            ]
          }
        },
        {
          $project: {
            name: 1,
            description: 1,
            city: 1,
            state: 1,
            email: '$userDetails.email',
            isApproved: 1,
            createdAt: 1
          }
        },
        { $limit: parseInt(limit) }
      ]);
    }

    // Search products
    if (entities.includes('products')) {
      searchResults.products = await Product.aggregate([
        {
          $lookup: {
            from: 'categories',
            localField: 'category',
            foreignField: '_id',
            as: 'categoryDetails'
          }
        },
        { $unwind: '$categoryDetails' },
        {
          $lookup: {
            from: 'providers',
            localField: 'provider',
            foreignField: '_id',
            as: 'providerDetails'
          }
        },
        { $unwind: '$providerDetails' },
        {
          $match: {
            $or: [
              { name: searchRegex },
              { description: searchRegex },
              { 'categoryDetails.name': searchRegex },
              { 'providerDetails.name': searchRegex }
            ]
          }
        },
        {
          $project: {
            name: 1,
            description: 1,
            categoryName: '$categoryDetails.name',
            providerName: '$providerDetails.name',
            photo: { $arrayElemAt: ['$photo', 0] },
            createdAt: 1
          }
        },
        { $limit: parseInt(limit) }
      ]);
    }

    // Search orders
    if (entities.includes('orders')) {
      searchResults.orders = await Order.aggregate([
        {
          $lookup: {
            from: 'users',
            localField: 'user_id',
            foreignField: '_id',
            as: 'userDetails'
          }
        },
        { $unwind: '$userDetails' },
        {
          $lookup: {
            from: 'products',
            localField: 'product_id',
            foreignField: '_id',
            as: 'productDetails'
          }
        },
        { $unwind: '$productDetails' },
        {
          $lookup: {
            from: 'providers',
            localField: 'provider_id',
            foreignField: '_id',
            as: 'providerDetails'
          }
        },
        { $unwind: '$providerDetails' },
        {
          $match: {
            $or: [
              { 'userDetails.email': searchRegex },
              { 'productDetails.name': searchRegex },
              { 'providerDetails.name': searchRegex },
              { status: searchRegex }
            ]
          }
        },
        {
          $project: {
            customerEmail: '$userDetails.email',
            productName: '$productDetails.name',
            providerName: '$providerDetails.name',
            status: 1,
            total_price: 1,
            quantity: 1,
            createdAt: 1
          }
        },
        { $limit: parseInt(limit) }
      ]);
    }
    const result = {
      query,
      searchedEntities: entities,
      results: searchResults,
      totalResults: Object.values(searchResults).reduce((sum, arr) => sum + arr.length, 0)
    };

    sendSuccess(res, 'Advanced search completed successfully', httpStatus.OK, result);
  } catch (error) {
    console.error('Advanced search error:', error);
    sendError(res, 'Failed to perform advanced search', httpStatus.INTERNAL_SERVER_ERROR);
  }
});

/**
 * Helper function to generate summary report
 */
const getSummaryReport = async () => {
  const [
    totalUsers,
    totalOrders,
    totalRevenue,
    avgRating,
    topCategory
  ] = await Promise.all([
    User.countDocuments(),
    Order.countDocuments(),
    Order.aggregate([{ $group: { _id: null, total: { $sum: '$total_price' } } }]),
    Review.aggregate([{ $group: { _id: null, avg: { $avg: '$rating' } } }]),
    Order.aggregate([
      {
        $lookup: {
          from: 'products',
          localField: 'product_id',
          foreignField: '_id',
          as: 'product'
        }
      },
      { $unwind: '$product' },
      {
        $lookup: {
          from: 'categories',
          localField: 'product.category',
          foreignField: '_id',
          as: 'category'
        }
      },
      { $unwind: '$category' },
      {
        $group: {
          _id: '$category.name',
          count: { $sum: 1 }
        }
      },
      { $sort: { count: -1 } },
      { $limit: 1 }
    ])
  ]);

  return {
    totalUsers,
    totalOrders,
    totalRevenue: totalRevenue[0]?.total || 0,
    averageRating: Math.round((avgRating[0]?.avg || 0) * 10) / 10,
    topCategory: topCategory[0]?._id || 'N/A'
  };
};

/**
 * Helper function to generate sales report
 */
const getSalesReport = async () => {
  const salesData = await Order.aggregate([
    {
      $group: {
        _id: {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' }
        },
        totalSales: { $sum: '$total_price' },
        orderCount: { $sum: 1 },
        avgOrderValue: { $avg: '$total_price' }
      }
    },
    { $sort: { '_id.year': -1, '_id.month': -1 } },
    { $limit: 12 }
  ]);

  return { salesData };
};

/**
 * Helper function to generate users report
 */
const getUsersReport = async () => {
  const [customerStats, providerStats] = await Promise.all([
    User.aggregate([
      { $match: { role: 'customer' } },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': -1, '_id.month': -1 } },
      { $limit: 12 }
    ]),
    Provider.aggregate([
      {
        $group: {
          _id: '$isApproved',
          count: { $sum: 1 }
        }
      }
    ])
  ]);

  return { customerStats, providerStats };
};

/**
 * Helper function to generate providers report
 */
const getProvidersReport = async () => {
  const providerData = await Provider.aggregate([
    {
      $lookup: {
        from: 'orders',
        localField: '_id',
        foreignField: 'provider_id',
        as: 'orders'
      }
    },
    {
      $addFields: {
        totalOrders: { $size: '$orders' },
        totalRevenue: { $sum: '$orders.total_price' }
      }
    },
    {
      $project: {
        name: 1,
        isApproved: 1,
        city: 1,
        state: 1,
        totalOrders: 1,
        totalRevenue: 1,
        createdAt: 1
      }
    },
    { $sort: { totalRevenue: -1 } }
  ]);

  return { providerData };
};

/**
 * Helper function to generate system health report
 */
const getSystemHealthReport = async () => {
  const [
    dbStats,
    recentErrors,
    activeUsers
  ] = await Promise.all([
    // Database collection stats
    Promise.all([
      User.countDocuments(),
      Product.countDocuments(),
      Order.countDocuments(),
      Review.countDocuments()
    ]).then(([users, products, orders, reviews]) => ({
      users, products, orders, reviews
    })),
    // Recent error logs (if you have error logging)
    [],
    // Active users in last 24 hours (based on recent orders/activities)
    Order.countDocuments({
      createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
    })
  ]);

  return {
    databaseStats: dbStats,
    recentErrors,
    activeUsersLast24h: activeUsers,
    systemStatus: 'healthy'
  };
};

module.exports = {
  getAnalytics,
  getReports,
  advancedSearch
};
