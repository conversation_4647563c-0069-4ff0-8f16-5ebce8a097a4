const passport = require('passport');
const httpStatus = require('http-status');
const ApiError = require('../utils/ApiError');
const { roleRights } = require('../config/roles');
const { ObjectId } = require('mongodb');
const { findByColumnName } = require('../services/GlobalService');

const verifyCallback = (req, resolve, reject, requiredRights) => async (err, user, info) => {
  if (err || info || !user) {
    return reject(new ApiError(httpStatus.UNAUTHORIZED, 'Please authenticate'));
  }
  req.user = user;
  if (user.role == 'customer') {
    req.customer = await findByColumnName('Customer', { user: user._id });
  } else if (user.role == 'provider') {
    // Use lean query for better performance
    const { Provider } = require('../models');
    let provider = await Provider.findOne({ user: user._id }).lean() ?? {};
    if (!(req.path === '/profile' && (req.method === 'GET' || req.method === 'POST'))) {
      if (!provider.hasOwnProperty('isApproved'))
        return reject(new ApiError(httpStatus.NOT_ACCEPTABLE, 'Please complete your profile'));
      else if (provider.isApproved != 'approved')
        return reject(new ApiError(httpStatus.NOT_ACCEPTABLE, 'Please wait for admin approval, currently your status is, ' + provider.isApproved));
    }
    req.provider = provider;
  }
  if (requiredRights.length) {
    const userRights = roleRights.get(user.role);
    const hasRequiredRights = requiredRights.every((requiredRight) => userRights.includes(requiredRight));
    if (!hasRequiredRights && req.params.userId !== user.id) {
      return reject(new ApiError(httpStatus.FORBIDDEN, 'Forbidden'));
    }
  }

  resolve();
};

const auth =
  (...requiredRights) =>
  async (req, res, next) => {
    return new Promise((resolve, reject) => {
      passport.authenticate('jwt', { session: false }, verifyCallback(req, resolve, reject, requiredRights))(req, res, next);
    })
      .then(() => next())
      .catch((err) => next(err));
  };

module.exports = auth;
