const express = require('express');
const validate = require('../../middlewares/validate');
const { categoryController, customerController, providerController, questionController, productController, orderController, reviewController, notify<PERSON><PERSON>roller, adminDashboardController } = require('../../controllers');
const { categoryValidation, customerValidation, providerValidation, questionValidation, productValidation } = require('../../validations');
const auth = require('../../middlewares/auth');
const fileUpload = require('../../middlewares/fileUpload');
const { createAdminQueryFilter } = require('../../middlewares/adminOptimization');

const router = express.Router();

// ############## Category Routes ##############
router.get('/category', auth('adminRootAccess'), createAdminQueryFilter('Category'), categoryController.index);
router.get('/category/:category', auth('adminRootAccess'), validate(categoryValidation.params), categoryController.view);
router.patch('/category', auth('adminRootAccess'), validate(categoryValidation.body), categoryController.status);
router.delete('/category', auth('adminRootAccess'), validate(categoryValidation.body), categoryController.softDelete);
router.post('/category', auth('adminRootAccess'), validate(categoryValidation.create), categoryController.create);
router.put('/category', auth('adminRootAccess'), validate(categoryValidation.update), categoryController.update);
// router.patch('/sub-category', auth('adminRootAccess'), validate(categoryValidation.subCategory), categoryController.subCategoryStatus);
// router.delete('/sub-category', auth('adminRootAccess'), validate(categoryValidation.subCategory), categoryController.subCategorySoftDelete);

// ############## Question Routes ##############
router.get('/question', auth('adminRootAccess'), createAdminQueryFilter('Question'), questionController.index);
// router.get('/question/:question', auth('adminRootAccess'), validate(questionValidation.params), questionController.view);
router.post('/question', auth('adminRootAccess'), validate(questionValidation.create), questionController.create);
router.put('/question', auth('adminRootAccess'), validate(questionValidation.update), questionController.update);
router.delete('/question', auth('adminRootAccess'), validate(questionValidation.body), questionController.softDelete);
// router.post('/option', auth('adminRootAccess'), validate(questionValidation.createOptions), questionController.optionsCreate);
// router.put('/option', auth('adminRootAccess'), validate(questionValidation.updateOption), questionController.optionUpdate);
// router.delete('/option', auth('adminRootAccess'), validate(questionValidation.deleteOption), questionController.optionSoftDelete);

// ############## Customer Routes ##############
router.get('/customer', auth('adminRootAccess'), createAdminQueryFilter('Customer'), customerController.index);
router.post('/customer', auth('adminRootAccess'),
  fileUpload('uploads/customers', [{ fieldName: 'photo', allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/svg+xml'], minSize: 20 * 1024, maxSize: 2000 * 1024, fileRequired: false, multiple: false,}]),
  validate(customerValidation.create),
  customerController.create
);
router.put('/customer', auth('adminRootAccess'),
  fileUpload('uploads/customers', [{ fieldName: 'photo', allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/svg+xml'], minSize: 20 * 1024, maxSize: 2000 * 1024, fileRequired: false, multiple: false }]),
  validate(customerValidation.update),
  customerController.update
);
router.delete('/customer/:customer', auth('adminRootAccess'), validate(customerValidation.params), customerController.softDelete);

// ############## Provider Routes ##############
router.get('/provider', auth('adminRootAccess'), createAdminQueryFilter('Provider'), providerController.index);
router.patch('/provider/status', auth('adminRootAccess'), validate(providerValidation.status), providerController.status);
router.get('/provider/:provider', auth('adminRootAccess'), validate(providerValidation.params), providerController.view);
router.post('/provider', auth('adminRootAccess'), fileUpload('uploads/providers', [
  { fieldName: 'photoId', allowedTypes: [ 'image/jpeg', 'image/jpg', 'image/png', 'application/pdf'], minSize: 20 * 1024, maxSize: 2000 * 1024, fileRequired: true, multiple: false, },
  { fieldName: 'cannabisLicense', allowedTypes: [ 'image/jpeg', 'image/jpg', 'image/png', 'application/pdf', ], minSize: 20 * 1024, maxSize: 2000 * 1024, fileRequired: true, multiple: false, },
  { fieldName: 'resellersPermit', allowedTypes: [ 'image/jpeg', 'image/jpg', 'image/png', 'application/pdf', ], minSize: 20 * 1024, maxSize: 2000 * 1024, fileRequired: true, multiple: false, },
]),  validate(providerValidation.create), providerController.create
);
router.patch( '/provider', auth('adminRootAccess'), fileUpload('uploads/providers', [
  { fieldName: 'photoId', allowedTypes: [ 'image/jpeg', 'image/jpg', 'image/png', 'application/pdf', ], minSize: 20 * 1024, maxSize: 2000 * 1024, fileRequired: false, multiple: false, },
  { fieldName: 'cannabisLicense', allowedTypes: [ 'image/jpeg', 'image/jpg', 'image/png', 'application/pdf', ], minSize: 20 * 1024, maxSize: 2000 * 1024, fileRequired: false, multiple: false, },
  { fieldName: 'resellersPermit', allowedTypes: [ 'image/jpeg', 'image/jpg', 'image/png', 'application/pdf', ], minSize: 20 * 1024, maxSize: 2000 * 1024, fileRequired: false, multiple: false, }
]), validate(providerValidation.update), providerController.update
);
router.delete('/provider/:provider', auth('adminRootAccess'), validate(providerValidation.params), providerController.softDelete);

// ############## Product Routes ##############
router.get('/product', auth('adminRootAccess'), createAdminQueryFilter('Product'), productController.index);
router.get('/product/:product', auth('adminRootAccess'), validate(productValidation.params), productController.view);
router.post('/product', auth('adminRootAccess'),
  fileUpload('uploads/products', [{ fieldName: 'photo', allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/svg+xml'], minSize: 20 * 1024, maxSize: 2000 * 1024, fileRequired: true, multiple: true }]),
  validate(productValidation.create), productController.create);
router.put('/product', auth('adminRootAccess'),
  fileUpload('uploads/products', [{ fieldName: 'photo', allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/svg+xml'], minSize: 20 * 1024, maxSize: 2000 * 1024, fileRequired: false, multiple: true }]),
  validate(productValidation.update), productController.update);
router.delete('/product', auth('adminRootAccess'), validate(productValidation.body), productController.softDelete);


// ########################## Order Routes #######################
router.get('/orders', auth('adminRootAccess'), createAdminQueryFilter('Order'), orderController.index);
router.get('/dashboard', auth('adminRootAccess'), customerController.getDashboardCounts);
router.get('/reviews', auth('adminRootAccess'), createAdminQueryFilter('Review'), reviewController.getReviews);

// ########################## Notification Routes #######################
router.get('/notifications', auth('adminRootAccess'), createAdminQueryFilter('Notification'), notifyController.getNotifications);

// ########################## Admin Dashboard Routes #######################
router.get('/analytics', auth('adminRootAccess'), adminDashboardController.getAnalytics);
router.get('/reports', auth('adminRootAccess'), adminDashboardController.getReports);
router.get('/search', auth('adminRootAccess'), adminDashboardController.advancedSearch);

module.exports = router;
